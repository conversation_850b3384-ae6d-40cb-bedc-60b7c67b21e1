import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface RevenueItem {
  name: string;
  actualYTD: number;
  budgetYTD: number;
  dollarVariance: number;
  percentVariance: number;
  forecastFY: number;
  budgetFY: number;
  dollarVarianceFY: number;
  percentVarianceFY: number;
}

export interface PTDRevenueItem {
  name: string;
  ptdActual: number;
  ptdBudget: number;
  ptdVariance: number;
  ptdVariancePercent: number;
  ytdActual: number;
  ytdBudget: number;
  ytdVariance: number;
  ytdVariancePercent: number;
}

export interface ExpenseItem {
  name: string;
  actualYTD: number;
  budgetYTD: number;
  dollarVariance: number;
  percentVariance: number;
  forecastFY: number;
  budgetFY: number;
  dollarVarianceFY: number;
  percentVarianceFY: number;
}

export interface StatementOperation {
  name: string;
  description?: string;
  actual: number;
  budget: number;
  dollarVariance: number;
  forecast: number;
  budgetFY: number;
  dollarVarianceFY: number;
}

export interface ManagedUnit {
  name: string;
  property: string;
  propertyAlias: string;
  rpm: string;
  actual: number;
  budget: number;
  forecast: number;
}

export interface WAUnit {
  name: string;
  property: string;
  propertyAlias: string;
  rpm: string;
  actual: number;
  budget: number;
  forecast: number;
}

export interface UnitsReportItem {
  adminName: string;
  address: string;
  city: string;
  state: string;
  businessType: string;
  department: string;
  marketLeader: string;
  propertyBU: string;
  AdminBU: string;
  property_name: string;
  month: number;
  year: number;
  rpm: string;
  umonth: string;
  typee: string;
  actuals: number | null;
  Budget: number | null;
  Forecast: number | null;
}

export interface ReportFilters {
  year: string;
  month: string[];
  businessType: string[];
  department: string[];
  marketLeader: string[];
  adminBU: string[];
}

export interface CategoryTotals {
  actualYTD: number;
  budgetYTD: number;
  dollarVariance: number;
  percentVariance: number;
  forecastFY: number;
  budgetFY: number;
  dollarVarianceFY: number;
  percentVarianceFY: number;
}

export interface PTDCategoryTotals {
  ptdActual: number;
  ptdBudget: number;
  ptdVariance: number;
  ptdVariancePercent: number;
  ytdActual: number;
  ytdBudget: number;
  ytdVariance: number;
  ytdVariancePercent: number;
}

export interface CategoryData {
  title: string;
  items: RevenueItem[] | ExpenseItem[];
  total: CategoryTotals;
}

export interface PTDCategoryData {
  title: string;
  items: PTDRevenueItem[];
  total: PTDCategoryTotals;
}

export interface IncomeReportState {
  title: string;
  subtitle: string;
  lastClosedMonth: string;
  managedUnits: ManagedUnit[];
  unitsReport: UnitsReportItem[];
  statementOfOperations: StatementOperation[];
  incomeReport: CategoryData[];
  ptdReport: PTDCategoryData[];
  filters: ReportFilters;
  expandedCategories: Record<string, boolean>;
  expandedLineItems: Record<string, boolean>;
  loading: boolean;
  error: string | null;
}

const initialState: IncomeReportState = {
  title: 'Statement of Operations',
  subtitle: 'Willowbridge All Properties',
  lastClosedMonth: 'January 2025',
  managedUnits: [],
  unitsReport: [],
  statementOfOperations: [],
  incomeReport: [],
  ptdReport: [],
  filters: {
    year: '2025',
    month: ['January'],
    businessType: [],
    department: [],
    marketLeader: [],
    adminBU: [],
  },
  expandedCategories: {
    'TOTAL REVENUE': true,
    'TOTAL EXPENSES': true,
    // Units: true,
    'ADD BACK:': true,
    // 'EBITDA MARGIN': true,
    'ADD BACK: NON-RECURRING ITEMS': true,
    // 'ADJUSTED EBITDA MARGIN': true,
    'RETURN ON REVENUE': true,
  },
  expandedLineItems: {},
  loading: false,
  error: null,
};

export const incomeReportSlice = createSlice({
  name: 'incomeReport',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setIncomeReport: (
      state,
      action: PayloadAction<{
        categoryData: CategoryData[];
        lastMonth: string;
      }>,
    ) => {
      state.incomeReport = action.payload.categoryData;
      state.lastClosedMonth = action.payload.lastMonth;
    },
    setPTDReport: (
      state,
      action: PayloadAction<{
        categoryData: PTDCategoryData[];
        lastMonth: string;
      }>,
    ) => {
      state.ptdReport = action.payload.categoryData;
      state.lastClosedMonth = action.payload.lastMonth;
    },
    setStatementOfOperations: (
      state,
      action: PayloadAction<{
        operationsData: StatementOperation[];
        lastMonth: string;
      }>,
    ) => {
      state.statementOfOperations = action.payload.operationsData;
      state.lastClosedMonth = action.payload.lastMonth;
    },
    setUnitsReport: (state, action: PayloadAction<UnitsReportItem[]>) => {
      state.unitsReport = action.payload;
    },
    setLastClosedMonth: (state, action: PayloadAction<string>) => {
      state.lastClosedMonth = action.payload;
    },
    setFilters: (state, action: PayloadAction<ReportFilters>) => {
      state.filters = action.payload;
      // state.loading = true;
    },
    toggleCategoryExpansion: (state, action: PayloadAction<string>) => {
      const categoryTitle = action.payload;
      state.expandedCategories[categoryTitle] =
        !state.expandedCategories[categoryTitle];
    },
    toggleLineItemExpansion: (state, action: PayloadAction<string>) => {
      const lineItemKey = action.payload;
      state.expandedLineItems[lineItemKey] =
        !state.expandedLineItems[lineItemKey];
    },
    expandAllLineItems: (state, action: PayloadAction<string[]>) => {
      const lineItemKeys = action.payload;
      lineItemKeys.forEach((key) => {
        state.expandedLineItems[key] = true;
      });
    },
    collapseAllLineItems: (state) => {
      state.expandedLineItems = {};
    },
  },
});

export const {
  setLoading,
  setError,
  setIncomeReport,
  setPTDReport,
  setFilters,
  toggleCategoryExpansion,
  toggleLineItemExpansion,
  expandAllLineItems,
  collapseAllLineItems,
  setStatementOfOperations,
  setUnitsReport,
  setLastClosedMonth,
} = incomeReportSlice.actions;

export default incomeReportSlice;
