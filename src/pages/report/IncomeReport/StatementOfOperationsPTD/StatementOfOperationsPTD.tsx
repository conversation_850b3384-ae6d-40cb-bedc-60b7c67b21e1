import React from 'react';
import { getStatementOfOperationsPTD } from '@/api/statementOfOperationsApi';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Container } from '@/components/common/container';
import ExpandCollapseAllButton from '../components/ExpandCollapseAllButton';
import ReportFilters from '../components/ReportFilters';
import { createPTDReportColumns } from '../components/ReportTable';
import StatementErrorState from '../components/StatementErrorState';
import StatementExportButtons from '../components/StatementExportButtons';
import StatementLoadingState from '../components/StatementLoadingState';
import StatementTable from '../components/StatementTable';
import { GroupedLineItemDetails } from '../hooks/useLineItemDetails';
import { useLineItemDetailsPTD } from '../hooks/useLineItemDetailsPTD';
import { useStatementDataWithState } from '../hooks/useStatementData';
import {
  BaseCategoryTotals,
  PTDCategoryData,
  PTDCategoryTotals,
  ThousandCategoryTotals,
} from '../types/statementTypes';
import { formatPTDStatementData } from '../utils/dataTransformers';
import { exportPTDToExcel, exportPTDToPDF } from '../utils/formatters';
import {
  convertPTDTotalForReportTable,
  formatTableCellValue,
} from '../utils/formatting';
import { sortCategories } from '../utils/sorting';

const StatementOfOperationsPTD: React.FC = () => {
  const navigate = useNavigate();

  const { loading, error, expandedCategories, expandedLineItems, filters } =
    useSelector((state: RootState) => ({
      loading: state.incomeReport.loading,
      error: state.incomeReport.error,
      expandedCategories: state.incomeReport.expandedCategories,
      expandedLineItems: state.incomeReport.expandedLineItems,
      filters: state.incomeReport.filters,
    }));

  const statementData = useStatementDataWithState(
    getStatementOfOperationsPTD,
    formatPTDStatementData,
  );

  const { lineItemDetails } = useLineItemDetailsPTD();

  const handleExportToExcel = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(
      statementData.categoryData,
    ) as PTDCategoryData[];
    exportPTDToExcel(
      sortedCategoryData,
      statementData.lastMonth,
      expandedCategories,
      expandedLineItems,
      lineItemDetails,
      filters,
    );
  };

  const handleExportToPDF = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(
      statementData.categoryData,
    ) as PTDCategoryData[];
    exportPTDToPDF(
      sortedCategoryData,
      statementData.lastMonth,
      expandedCategories,
      expandedLineItems,
      lineItemDetails,
      filters,
    );
  };

  const handleDrillThrough = () => {
    navigate('/ptd-drill-through');
  };

  const formatCellValue = (value: string | number, columnId: string) => {
    return formatTableCellValue(value, columnId);
  };

  const formatPTDPeriodDisplay = (months: string[], year: string): string => {
    if (months.length === 0) return `January ${year}`;
    if (months.length === 1) return `${months[0]} ${year}`;
    return `${months[0]} - ${months[months.length - 1]} ${year}`;
  };

  if (loading) {
    return <StatementLoadingState message="Loading PTD report data..." />;
  }

  if (error) {
    return <StatementErrorState error={error} />;
  }

  const ptdReportColumns = createPTDReportColumns();
  const sortedCategoryData = statementData
    ? sortCategories(statementData.categoryData)
    : [];

  return (
    <Container title="Statement of Operations PTD" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg">
          <div className="min-w-[1024px]">
            <div className="bg-[#F4F4FF] p-4 mb-4 rounded-t-lg flex justify-between items-center">
              <h3 className="text-lg font-semibold mb-2 text-[#43298F]">
                Period : {formatPTDPeriodDisplay(filters.month, filters.year)}
              </h3>
              <div className="flex items-center gap-4">
                <ExpandCollapseAllButton categories={sortedCategoryData} />
                <StatementExportButtons
                  onExportExcel={handleExportToExcel}
                  onExportPDF={handleExportToPDF}
                  onDrillThrough={handleDrillThrough}
                  disabled={sortedCategoryData.length === 0}
                />
              </div>
            </div>

            <div className="p-4">
              <StatementTable
                categories={sortedCategoryData}
                columns={ptdReportColumns}
                expandedCategories={expandedCategories}
                expandedLineItems={expandedLineItems}
                lineItemDetails={lineItemDetails as GroupedLineItemDetails}
                leftGroupTitle="Period To Date"
                rightGroupTitle="Year To Date"
                year={filters?.year || '2025'}
                convertTotalForReportTable={
                  convertPTDTotalForReportTable as (
                    total:
                      | BaseCategoryTotals
                      | PTDCategoryTotals
                      | ThousandCategoryTotals,
                    categoryTitle?: string,
                  ) => Record<string, number | string>
                }
                formatCellValue={formatCellValue}
              />
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default StatementOfOperationsPTD;
