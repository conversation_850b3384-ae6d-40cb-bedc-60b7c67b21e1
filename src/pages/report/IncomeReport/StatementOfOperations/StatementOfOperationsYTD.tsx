import React from 'react';
import { getStatementOfOperations } from '@/api/statementOfOperationsApi';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Container } from '@/components/common/container';
import ReportFilters from '../components/ReportFilters';
import { createIncomeReportColumns } from '../components/ReportTable';
import StatementErrorState from '../components/StatementErrorState';
import StatementExportButtons from '../components/StatementExportButtons';
import StatementLoadingState from '../components/StatementLoadingState';
import StatementTable from '../components/StatementTable';
import ExpandCollapseAllButton from '../components/ExpandCollapseAllButton';
import { useLineItemDetails } from '../hooks/useLineItemDetails';
import { useStatementDataWithState } from '../hooks/useStatementData';
import { formatYTDStatementData } from '../utils/dataTransformers';
import { exportToExcel, exportToPDF } from '../utils/formatters';
import {
  convertYTDTotalForReportTable,
  formatTableCellValue,
} from '../utils/formatting';
import { formatPeriodDisplay, sortCategories } from '../utils/sorting';

const StatementOfOperationsYTD: React.FC = () => {
  const navigate = useNavigate();

  const { loading, error, expandedCategories, expandedLineItems, filters } =
    useSelector((state: RootState) => ({
      loading: state.incomeReport.loading,
      error: state.incomeReport.error,
      expandedCategories: state.incomeReport.expandedCategories,
      expandedLineItems: state.incomeReport.expandedLineItems,
      filters: state.incomeReport.filters,
    }));

  const statementData = useStatementDataWithState(
    getStatementOfOperations,
    formatYTDStatementData,
  );

  const { lineItemDetails } = useLineItemDetails();

  const handleExportToExcel = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(statementData.categoryData);
    exportToExcel(
      sortedCategoryData,
      statementData.lastMonth,
      expandedCategories,
      expandedLineItems,
      lineItemDetails,
      filters,
    );
  };

  const handleExportToPDF = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(statementData.categoryData);
    exportToPDF(
      sortedCategoryData,
      statementData.lastMonth,
      expandedCategories,
      expandedLineItems,
      lineItemDetails,
      filters,
    );
  };

  const handleDrillThrough = () => {
    navigate('/ytd-drill-through');
  };

  const formatCellValue = (value: string | number, columnId: string) => {
    return formatTableCellValue(value, columnId);
  };

  if (loading) {
    return <StatementLoadingState />;
  }

  if (error) {
    return <StatementErrorState error={error} />;
  }

  const incomeReportColumns = createIncomeReportColumns();
  const sortedCategoryData = statementData
    ? sortCategories(statementData.categoryData)
    : [];

  return (
    <Container title="Statement of Operations YTD" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg">
          <div className="min-w-[1024px]">
            <div className="bg-[#F4F4FF] p-4 mb-4 rounded-t-lg flex justify-between items-center">
              <h3 className="text-lg font-semibold mb-2 text-[#43298F]">
                Period : {formatPeriodDisplay(filters.month, filters.year)}
              </h3>
              <div className="flex items-center gap-4">
                <ExpandCollapseAllButton categories={sortedCategoryData} />
                <StatementExportButtons
                  onExportExcel={handleExportToExcel}
                  onExportPDF={handleExportToPDF}
                  onDrillThrough={handleDrillThrough}
                  disabled={sortedCategoryData.length === 0}
                />
              </div>
            </div>

            <div className="p-4">
              <StatementTable
                categories={sortedCategoryData}
                columns={incomeReportColumns}
                expandedCategories={expandedCategories}
                expandedLineItems={expandedLineItems}
                lineItemDetails={lineItemDetails}
                leftGroupTitle="Year to Date"
                rightGroupTitle="Full Year"
                year={filters?.year || '2025'}
                convertTotalForReportTable={convertYTDTotalForReportTable}
                formatCellValue={formatCellValue}
              />
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default StatementOfOperationsYTD;
